from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel


# Enum for chat types
class ChatType(str, Enum):
    CHAT_TYPE_UNSPECIFIED = "CHAT_TYPE_UNSPECIFIED"
    CHAT_TYPE_AGENT = "CHAT_TYPE_AGENT"
    CHAT_TYPE_GLOBAL = "CHAT_TYPE_GLOBAL"


# Enum for sender types
class SenderType(str, Enum):
    SENDER_TYPE_UNSPECIFIED = "SENDER_TYPE_UNSPECIFIED"
    SENDER_TYPE_USER = "SENDER_TYPE_USER"
    SENDER_TYPE_ASSISTANT = "SENDER_TYPE_ASSISTANT"


# Enum for task statuses
class TaskStatus(str, Enum):
    TASK_STATUS_UNSPECIFIED = "TASK_STATUS_UNSPECIFIED"
    TASK_STATUS_RUNNING = "TASK_STATUS_RUNNING"
    TASK_STATUS_COMPLETED = "TASK_STATUS_COMPLETED"
    TASK_STATUS_FAILED = "TASK_STATUS_FAILED"
    TASK_STATUS_PAUSED = "TASK_STATUS_PAUSED"
    TASK_STATUS_CANCELLED = "TASK_STATUS_CANCELLED"


# Conversation base model
class ConversationBase(BaseModel):
    chatType: ChatType


# Conversation create model
class ConversationCreate(ConversationBase):
    agentId: Optional[str] = None


# Conversation response model
class ConversationResponse(ConversationBase):
    id: str
    userId: str
    agentId: Optional[str] = None
    inputTokens: int = 0
    outputTokens: int = 0
    createdAt: datetime
    updatedAt: datetime
    title: Optional[str] = None
    tasks: List["TaskResponse"] = []


# Update conversation tokens request model
class UpdateConversationTokensRequest(BaseModel):
    conversationId: str
    inputTokens: Optional[int] = None
    outputTokens: Optional[int] = None


# Pagination metadata
class PaginationMetadata(BaseModel):
    total: int
    totalPages: int
    currentPage: int
    pageSize: int
    hasNextPage: bool
    hasPreviousPage: bool


# List of conversations
class ConversationList(BaseModel):
    data: List[ConversationResponse]
    metadata: PaginationMetadata


# Message base model
class MessageBase(BaseModel):
    conversationId: str
    senderType: SenderType
    data: Optional[Dict[str, Any]] = None
    workflowId: Optional[str] = None
    workflowResponse: Optional[Dict[str, dict]] = None


# Message create model
class MessageCreate(MessageBase):
    pass


# Message response model
class MessageResponse(MessageBase):
    id: str
    createdAt: datetime
    updatedAt: datetime


# Message list model
class MessageList(BaseModel):
    data: List[MessageResponse]
    metadata: PaginationMetadata


# Task base model
class TaskBase(BaseModel):
    title: str
    globalChatConversationId: str
    agentConversationId: str
    agentId: str
    correlationId: Optional[str] = None
    taskStatus: TaskStatus
    sessionId: Optional[str] = None


# Task create model
class TaskCreate(TaskBase):
    pass


# Task response model
class TaskResponse(TaskBase):
    id: str
    createdAt: datetime
    updatedAt: datetime


# Task list model
class TaskList(BaseModel):
    data: List[TaskResponse]
    metadata: PaginationMetadata


# Task delegation request model
class TaskDelegationRequest(BaseModel):
    title: str
    globalChatConversationId: str
    agentId: str
    correlationId: Optional[str] = None
    sessionId: Optional[str] = None


# Task delegation response model
class TaskDelegationResponse(BaseModel):
    success: bool
    message: str
    data: Dict[str, Any]  # Contains both task and agentConversation data


# Update task status request model
class UpdateTaskStatusRequest(BaseModel):
    taskId: str
    taskStatus: TaskStatus


# Resolve forward references
ConversationResponse.model_rebuild()
