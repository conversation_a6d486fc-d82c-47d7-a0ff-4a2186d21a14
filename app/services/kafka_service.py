"""
Kafka service for communicating with agents.

This module provides a service for sending and receiving messages to/from agents via Kafka.
"""

import asyncio
import json
import uuid
from typing import Any, Dict, List, Optional

from aiokafka import AIOKafkaConsumer, AIOKafkaProducer  # type: ignore
from pydantic import BaseModel

from app.core.config import settings
from app.core.logging import get_logger

# Route to SSE manager for streaming
from app.helper.sse_manager import SseManager

from ..shared.constants import ContentType, SSEEventType
from ..shared.enums.chat_enums import ChatMode

# Configure logging
logger = get_logger(__name__)

# Kafka topics
KAFKA_AGENT_CREATION_TOPIC = settings.KAFKA_AGENT_CREATION_TOPIC
KAFKA_AGENT_CHAT_TOPIC = settings.KAFKA_AGENT_CHAT_TOPIC
KAFKA_AGENT_RESPONSE_TOPIC = settings.KAFKA_AGENT_RESPONSE_TOPIC
KAFKA_AGENT_QUERY_TOPIC = settings.KAFKA_AGENT_QUERY_TOPIC
KAFKA_AGENT_SESSION_DELETION_TOPIC = settings.KAFKA_AGENT_SESSION_DELETION_TOPIC
KAFKA_AGENT_CHAT_STOP_TOPIC = settings.KAFKA_AGENT_CHAT_STOP_TOPIC

# Orchestration Team Kafka topics
KAFKA_ORCHESTRATION_TEAM_SESSION_TOPIC = settings.KAFKA_ORCHESTRATION_TEAM_SESSION_TOPIC
KAFKA_ORCHESTRATION_TEAM_CHAT_TOPIC = settings.KAFKA_ORCHESTRATION_TEAM_CHAT_TOPIC
KAFKA_HUMAN_INPUT_RESPONSE_TOPIC = settings.KAFKA_HUMAN_INPUT_RESPONSE_TOPIC
KAFKA_HUMAN_INPUT_REQUEST_TOPIC = settings.KAFKA_HUMAN_INPUT_REQUEST_TOPIC


def serialize_for_kafka(obj: Any) -> Any:
    """
    Recursively serialize objects for Kafka JSON serialization.

    Converts Pydantic models to dictionaries and handles nested structures.
    """
    if isinstance(obj, BaseModel):
        # Use model_dump for Pydantic v2 compatibility
        if hasattr(obj, "model_dump"):
            return obj.model_dump()
        else:
            # Fallback for Pydantic v1
            return obj.dict()
    elif isinstance(obj, dict):
        return {key: serialize_for_kafka(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [serialize_for_kafka(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(serialize_for_kafka(item) for item in obj)
    else:
        return obj


class KafkaService:
    """
    Service for communicating with agents via Kafka.
    """

    def __init__(self):
        """
        Initialize the Kafka service.
        """
        self.producer = None
        self.consumer = None
        self._initialized = False
        self.bootstrap_servers = settings.KAFKA_BOOTSTRAP_SERVERS or "localhost:9092"
        self.response_handlers: Dict[str, asyncio.Future] = {}
        self.consumer_task = None
        self.sse_manager = SseManager()

    async def initialize(self):
        """
        Initialize the Kafka producer and consumer.
        """
        if self._initialized:
            return

        try:
            # Initialize producer
            self.producer = AIOKafkaProducer(
                bootstrap_servers=self.bootstrap_servers,
                max_request_size=524288000,  # 500MB max request size
            )

            # Initialize consumer
            self.consumer = AIOKafkaConsumer(
                KAFKA_AGENT_RESPONSE_TOPIC,
                KAFKA_HUMAN_INPUT_REQUEST_TOPIC,
                bootstrap_servers=self.bootstrap_servers,
                group_id=f"api-gateway-{uuid.uuid4()}",
                auto_offset_reset="latest",
                enable_auto_commit=True,
            )

            # Start producer and consumer
            await self.producer.start()
            await self.consumer.start()

            # Start consumer task
            self.consumer_task = asyncio.create_task(self._consume_messages())

            self._initialized = True
            logger.info("Kafka service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Kafka service: {e}")
            await self.cleanup()
            raise

    async def cleanup(self):
        """
        Clean up Kafka resources.
        """
        if self.consumer_task:
            self.consumer_task.cancel()
            try:
                await self.consumer_task
            except asyncio.CancelledError:
                pass

        if self.producer:
            await self.producer.stop()

        if self.consumer:
            await self.consumer.stop()

        self._initialized = False
        logger.info("Kafka service cleaned up")

    async def send_message(
        self,
        topic: str,
        message: Dict[str, Any],
        correlation_id: str = None,
        reply_topic: str = KAFKA_AGENT_RESPONSE_TOPIC,
    ) -> str:
        """
        Send a message to a Kafka topic.

        Args:
            topic: The Kafka topic to send the message to
            message: The message to send
            correlation_id: The correlation ID for the message (generated if not provided)
            reply_topic: The topic to receive replies on
            client_id: The client ID for streaming responses (optional)

        Returns:
            The correlation ID for the message
        """
        if not self._initialized:
            await self.initialize()

        # Generate correlation ID if not provided
        if not correlation_id:
            correlation_id = str(uuid.uuid4())

        # Add correlation ID to message
        message["run_id"] = correlation_id

        # Prepare headers
        headers = [
            ("correlationId", correlation_id.encode("utf-8")),
            ("reply-topic", reply_topic.encode("utf-8")),
        ]

        try:
            # Serialize message for Kafka (handles Pydantic models)
            serialized_message = serialize_for_kafka(message)

            # Encode and send message
            value = json.dumps(serialized_message).encode("utf-8")
            await self.producer.send_and_wait(topic, value=value, headers=headers)
            logger.info(f"Message sent to topic {topic} with correlation ID {correlation_id}")
            return correlation_id
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise

    async def _consume_messages(self):
        """
        Consume messages from Kafka and route them to the appropriate handlers.
        """
        try:
            async for msg in self.consumer:
                try:
                    # Extract correlation ID and client ID from headers
                    correlation_id = None
                    if msg.headers:
                        headers_dict = {
                            k.decode("utf-8") if isinstance(k, bytes) else k: (
                                v.decode("utf-8") if isinstance(v, bytes) else v
                            )
                            for k, v in msg.headers
                        }
                        correlation_id = headers_dict.get("correlationId")

                    # Decode message
                    response = json.loads(msg.value.decode("utf-8"))

                    logger.info(
                        f"Received message with correlation ID {correlation_id}: {response}"
                    )

                    if response.get("event_type") == SSEEventType.SESSION_INITIALIZED.value:
                        self.sse_manager.send_session_initialized(
                            session_id=response["session_id"], client_id=response["session_id"]
                        )

                    if response.get("event_type") == SSEEventType.MESSAGE_STREAMING.value:
                        self.sse_manager.send_message_streaming(
                            content=response.get("agent_response").get("content"),
                            content_type=response.get("content_type", ContentType.TEXT_PLAIN.value),
                            message_type=response.get("agent_response").get("message_type"),
                            source=response.get("agent_response").get("source"),
                            metadata=response.get("agent_response").get("metadata"),
                            client_id=response["session_id"],
                        )

                    if response.get("event_type") == SSEEventType.MESSAGE_RESPONSE.value:
                        self.sse_manager.send_message_response(
                            content=response.get("agent_response"),
                            content_type=response.get("content_type", ContentType.TEXT_PLAIN.value),
                            client_id=response["session_id"],
                        )

                    # Also route to regular handler if exists (for backward compatibility)
                    if correlation_id and correlation_id in self.response_handlers:
                        future = self.response_handlers[correlation_id]
                        if not future.done():
                            if (
                                response.get("event_type") == SSEEventType.MESSAGE_RESPONSE.value
                                or response.get("event_type")
                                == SSEEventType.SESSION_INITIALIZED.value
                                or (
                                    response.get("agent_response")
                                    and response.get("agent_response").get("message_type") == "text"
                                    and response.get("agent_response").get("source") != "user"
                                )
                            ):
                                future.set_result(response)
                                logger.debug(
                                    f"Routed message to handler for correlation ID {correlation_id}"
                                )

                    if (
                        response.get("agent_response")
                        and response.get("agent_response").get("message_type") == "task_result"
                    ) or (
                        response.get("agent_response")
                        and response.get("agent_response").get("message_type")
                        == "user_input_request"
                    ):
                        self.sse_manager.send_message_end(
                            session_id=response["session_id"],
                            client_id=response["session_id"],
                        )

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode message: {e}")
                except Exception as e:
                    logger.error(f"Error processing message: {e}")

        except asyncio.CancelledError:
            logger.info("Consumer task cancelled")
        except Exception as e:
            logger.error(f"Error in consumer task: {e}")

    async def wait_for_response(self, correlation_id: str, timeout: int = 30) -> Dict[str, Any]:
        """
        Wait for a response with the given correlation ID.

        Args:
            correlation_id: The correlation ID to wait for
            timeout: The timeout in seconds

        Returns:
            The response message
        """
        if not self._initialized:
            await self.initialize()

        # Create future for response
        future = asyncio.Future()
        self.response_handlers[correlation_id] = future

        try:
            # Wait for response with timeout
            response = await asyncio.wait_for(future, timeout=timeout)
            return response
        except asyncio.TimeoutError:
            logger.warning(f"Timeout waiting for response with correlation ID {correlation_id}")
            raise
        finally:
            # Clean up handler
            self.response_handlers.pop(correlation_id, None)

    async def create_agent_session(
        self,
        agent_id: str,
        user_id: str,
        organization_id: str = None,
        use_knowledge: bool = False,
        conversation_messages: List[Dict[str, str]] = None,
    ) -> str:
        """
        Create a new agent session with optional conversation context.

        Args:
            agent_id: The ID of the agent
            user_id: The ID of the user
            conversation_messages: Optional list of conversation messages to initialize the session

        Returns:
            The session ID
        """
        # Prepare creation request
        creation_request = {
            "agent_id": agent_id,
            "user_id": user_id,
            "organization_id": organization_id,  # Default organization
            "communication_type": "single",
            "use_knowledge": use_knowledge,
            "variables": {},
        }

        # Add conversation context if provided
        if conversation_messages:
            creation_request["conversation_context"] = conversation_messages

        # Send request and get correlation ID
        correlation_id = await self.send_message(KAFKA_AGENT_CREATION_TOPIC, creation_request)

        # Wait for response
        response = await self.wait_for_response(correlation_id, timeout=60)

        # Extract session ID
        session_id = response.get("session_id")
        if not session_id:
            raise ValueError("No session ID in response")

        logger.info(f"Agent session created with ID {session_id}")
        return session_id

    async def send_chat_message(
        self, session_id: str, message: str, attachments: Optional[List] = None
    ) -> Dict[str, Any]:
        """
        Send a chat message to an agent.

        Args:
            session_id: The session ID
            message: The message to send
            attachments: Optional list of file attachments

        Returns:
            The agent's response
        """

        chat_request = {
            "session_id": session_id,
            "chat_context": [{"role": "user", "content": message}],
            "chat_response": "stream",
            "attachments": attachments,
        }

        # Send request and get correlation ID
        correlation_id = await self.send_message(KAFKA_AGENT_CHAT_TOPIC, chat_request)

        self.sse_manager.send_message_stream_started(client_id=session_id)

        # Wait for response
        response = await self.wait_for_response(correlation_id, timeout=600)

        return response

    async def stop_agent_chat_stream(self, session_id: str, user_id: str = None) -> Dict[str, Any]:
        """
        Stop an active agent chat stream.

        Args:
            session_id: The session ID to stop streaming for
            user_id: The ID of the user requesting the stop

        Returns:
            Confirmation of the stop request
        """
        # Generate unique run ID
        run_id = str(uuid.uuid4())

        # Prepare stop request
        stop_request = {
            "session_id": session_id,
            "run_id": run_id,
            "user_id": user_id or "api_user",
            "action": "stop_stream",
            "timestamp": None,  # Will be set by the receiving service
        }

        # Send request and get correlation ID
        correlation_id = await self.send_message(KAFKA_AGENT_CHAT_STOP_TOPIC, stop_request)

        logger.info(
            f"Stop stream request sent for session {session_id}",
            extra={
                "session_id": session_id,
                "user_id": user_id,
                "run_id": run_id,
                "correlation_id": correlation_id,
            },
        )

        return {
            "success": True,
            "message": "Stop stream request sent successfully",
            "session_id": session_id,
            "run_id": run_id,
        }

    async def execute_agent_task(self, agent_id: str, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a task with an agent.

        Args:
            agent_id: The ID of the agent
            task: The task to execute

        Returns:
            The task result
        """

        # Generate unique run ID
        run_id = str(uuid.uuid4())

        # Prepare task request
        task_request = {
            "agent_id": agent_id,
            "query": task.get("query", task.get("input", {}).get("question", "")),
            "run_id": run_id,
            "user_id": task.get("user_id", "test_user"),
            "organization_id": task.get("organization_id"),
            "variables": task.get("variables", {}),
        }

        # Send request and get correlation ID
        await self.send_message(KAFKA_AGENT_QUERY_TOPIC, task_request)

        # Wait for response
        response = await self.wait_for_response(correlation_id=run_id, timeout=120)

        return response

    async def query_agent(
        self,
        agent_id: str,
        query: str,
        user_id: str = None,
        organization_id: str = None,
        variables: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        Query an agent directly without session creation.

        Args:
            agent_id: The ID of the agent
            query: The query to send to the agent
            user_id: The ID of the user making the query
            organization_id: The ID of the organization
            variables: Variables to pass to the agent

        Returns:
            The agent's response
        """
        # Generate unique run ID
        run_id = str(uuid.uuid4())

        # Prepare query request
        query_request = {
            "agent_id": agent_id,
            "query": query,
            "run_id": run_id,
            "user_id": user_id or "api_user",
            "organization_id": organization_id,
            "variables": variables or {},
        }

        # Send request and get correlation ID
        await self.send_message(KAFKA_AGENT_QUERY_TOPIC, query_request)

        # Wait for response
        response = await self.wait_for_response(correlation_id=run_id, timeout=60)

        return response

    async def delete_agent_session(
        self,
        session_id: str,
        user_id: str = None,
        reason: str = "user_request",
        force: bool = False,
    ) -> Dict[str, Any]:
        """
        Delete an agent session.

        Args:
            session_id: The ID of the session to delete
            user_id: The ID of the user requesting deletion
            reason: Reason for deletion
            force: Whether to force delete even if session doesn't exist

        Returns:
            The deletion response
        """
        # Generate unique run ID
        run_id = str(uuid.uuid4())

        # Prepare deletion request
        deletion_request = {
            "session_id": session_id,
            "run_id": run_id,
            "user_id": user_id or "api_user",
            "reason": reason,
            "force": force,
        }

        # Send request and get correlation ID
        correlation_id = await self.send_message(
            KAFKA_AGENT_SESSION_DELETION_TOPIC, deletion_request
        )

        # Wait for response
        response = await self.wait_for_response(correlation_id, timeout=30)

        logger.info(
            f"Session deletion request processed for session {session_id}",
            extra={
                "session_id": session_id,
                "user_id": user_id,
                "reason": reason,
                "force": force,
                "success": response.get("success", False),
            },
        )

        return response

    async def create_orchestration_team_session(
        self,
        user_id: str,
        organization_id: str = None,
        conversation_messages: List[Dict[str, str]] = None,
        variables: Dict[str, Any] = None,
    ) -> str:
        """
        Create a new orchestration team session.

        Args:
            user_id: The ID of the user
            conversation_messages: Optional list of conversation messages to initialize the session
            variables: Variables to pass to the orchestration team

        Returns:
            The session ID
        """
        # Generate unique run ID
        run_id = str(uuid.uuid4())

        # Prepare creation request
        creation_request = {
            "user_id": user_id,
            "communication_type": "orchestration_team",
            "run_id": run_id,
            "organization_id": organization_id,  # Default organization
            "variables": variables or {},
        }

        # Add conversation context if provided
        if conversation_messages:
            creation_request["conversation_context"] = conversation_messages

        # Send request and get correlation ID
        correlation_id = await self.send_message(
            KAFKA_ORCHESTRATION_TEAM_SESSION_TOPIC, creation_request
        )

        # Wait for response
        response = await self.wait_for_response(correlation_id, timeout=60)

        # Extract session ID
        session_id = response.get("session_id")
        if not session_id:
            raise ValueError("No session ID in response")

        logger.info(f"Orchestration team session created with ID {session_id}")
        return session_id

    async def send_orchestration_team_message(
        self,
        session_id: str,
        message: str,
        attachments: Optional[List] = None,
        mode: ChatMode = ChatMode.ASK,
    ) -> Dict[str, Any]:
        """
        Send a message to the orchestration team.

        Args:
            session_id: The session ID
            message: The message to send
            attachments: Optional list of file attachments

        Returns:
            The team's response
        """
        # Generate unique run ID
        run_id = str(uuid.uuid4())

        chat_request = {
            "session_id": session_id,
            "run_id": run_id,
            "user_id": "api_user",  # Default user ID
            "user_message": message,
            "organization_id": "api_gateway_org",
            "variables": {},
            "attachments": attachments or [],
            "mode": mode,
        }

        # Send request and get correlation ID
        correlation_id = await self.send_message(KAFKA_ORCHESTRATION_TEAM_CHAT_TOPIC, chat_request)

        # Start streaming for the session
        self.sse_manager.send_message_stream_started(client_id=session_id)

        # Wait for response
        response = await self.wait_for_response(correlation_id, timeout=120)

        return response

    async def send_human_input_response(
        self,
        session_id: str,
        team_conversation_id: str,
        user_input: str,
    ) -> Dict[str, Any]:
        """
        Send human input response to the orchestration team.

        Args:
            session_id: The session ID
            team_conversation_id: The team conversation ID requiring input
            user_input: The human's input or decision
            selected_option: If options were provided, the selected option

        Returns:
            The response confirmation
        """
        # Generate unique run ID
        run_id = str(uuid.uuid4())

        response_request = {
            "session_id": session_id,
            "run_id": run_id,
            "team_conversation_id": team_conversation_id,
            "user_input": user_input,
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("session_id", session_id.encode("utf-8")),
            ("team_conversation_id", team_conversation_id.encode("utf-8")),
        ]

        # Send request
        await self.producer.send_and_wait(
            KAFKA_HUMAN_INPUT_RESPONSE_TOPIC,
            value=json.dumps(response_request).encode("utf-8"),
            headers=headers,
        )

        logger.info(f"Human input response sent for team conversation {team_conversation_id}")

        return {
            "success": True,
            "message": "Human input response sent successfully",
            "run_id": run_id,
        }


# Create a singleton instance
kafka_service = KafkaService()
