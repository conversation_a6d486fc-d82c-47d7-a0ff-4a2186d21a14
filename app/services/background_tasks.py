"""
Background task processing service for handling asynchronous operations.

This module provides functionality for processing chat messages and other
operations in the background while returning quick responses to users.
"""

import asyncio
import json
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from app.core.logging import get_logger
from app.services.communication_service import CommunicationServiceClient
from app.services.kafka_service import kafka_service
from app.shared.constants import SenderType
from app.utils.redis.redis_service import RedisService

from ..shared.enums.chat_enums import ChatMode

logger = get_logger(__name__)

# Initialize services
redis_service = RedisService()
communication_service = CommunicationServiceClient()


class BackgroundTaskManager:
    """
    Manager for handling background tasks like message processing.
    """

    def __init__(self):
        self.active_tasks: Dict[str, asyncio.Task] = {}

    async def process_chat_message_background(
        self,
        processing_id: str,
        session_id: str,
        message: str,
        conversation_id: str,
        user_id: str,
        mode: ChatMode = ChatMode.ASK,
        attachments: Optional[List] = None,
    ) -> None:
        """
        Process a chat message in the background.

        Args:
            processing_id: Unique identifier for tracking this processing
            session_id: The session ID
            message: The user's message
            conversation_id: The conversation ID
            user_id: The user ID
            attachments: Optional list of file attachments
        """
        try:
            # Update status to processing
            await self._update_processing_status(
                processing_id, "processing", created_at=datetime.now(timezone.utc).isoformat()
            )

            logger.info(
                "Starting background chat message processing",
                extra={
                    "processing_id": processing_id,
                    "session_id": session_id,
                    "user_id": user_id,
                    "conversation_id": conversation_id,
                },
            )

            # Check if this is an orchestration team session
            session_key = f"session:{session_id}"
            session_data = redis_service.get_data_from_redis(session_key, "data")

            if session_data and session_data.get("session_type") == "orchestration_team":
                # Send message to orchestration team
                response = await kafka_service.send_orchestration_team_message(
                    session_id=session_id,
                    message=message,
                    attachments=attachments,
                    mode=mode,
                )
            else:
                # Send message via Kafka and wait for response (including attachments if present)
                response = await kafka_service.send_chat_message(session_id, message, attachments)

            # Extract message from response
            agent_response = response.get("agent_response", {})
            content = agent_response.get("content", "")
            task_id = response.get("run_id")

            # Extract token usage from agent response
            models_usage = agent_response.get("models_usage", {})
            input_tokens = models_usage.get("prompt_tokens", 0)
            output_tokens = models_usage.get("completion_tokens", 0)

            # Update conversation tokens
            await communication_service.update_conversation_tokens(
                conversationId=conversation_id,
                userId=user_id,
                inputTokens=input_tokens,
                outputTokens=output_tokens
            )

            logger.debug(
                "Updated conversation token usage",
                extra={
                    "conversation_id": conversation_id,
                    "input_tokens": input_tokens,
                    "output_tokens": output_tokens
                }
            )

            # Store conversation messages in communication service
            await self._store_chat_messages(
                conversation_id=conversation_id,
                user_message=message,
                agent_response=content,
                user_id=user_id,
                attachments=attachments,
                mode=mode,
            )

            # Update status to completed
            await self._update_processing_status(
                processing_id,
                "completed",
                message=content,
                task_id=task_id,
                completed_at=datetime.now(timezone.utc).isoformat(),
            )

            logger.info(
                "Background chat message processing completed successfully",
                extra={
                    "processing_id": processing_id,
                    "session_id": session_id,
                    "user_id": user_id,
                    "conversation_id": conversation_id,
                    "task_id": task_id,
                },
            )

        except Exception as e:
            # Update status to failed
            await self._update_processing_status(
                processing_id,
                "failed",
                error=str(e),
                completed_at=datetime.now(timezone.utc).isoformat(),
            )

            logger.error(
                "Background chat message processing failed",
                extra={
                    "processing_id": processing_id,
                    "session_id": session_id,
                    "user_id": user_id,
                    "conversation_id": conversation_id,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
            )

        finally:
            # Clean up the task from active tasks
            self.active_tasks.pop(processing_id, None)

    async def start_chat_message_processing(
        self,
        session_id: str,
        message: str,
        conversation_id: str,
        user_id: str,
        mode: ChatMode = ChatMode.ASK,
        attachments: Optional[List] = None,
    ) -> str:
        """
        Start processing a chat message in the background.

        Args:
            session_id: The session ID
            message: The user's message
            conversation_id: The conversation ID
            user_id: The user ID
            attachments: Optional list of file attachments

        Returns:
            The processing ID for tracking
        """
        processing_id = f"proc_{uuid.uuid4()}"

        # Create and start the background task
        task = asyncio.create_task(
            self.process_chat_message_background(
                processing_id,
                session_id,
                message,
                conversation_id,
                user_id,
                mode,
                attachments,
            )
        )

        # Store the task for potential cancellation
        self.active_tasks[processing_id] = task

        logger.info(
            "Started background chat message processing",
            extra={
                "processing_id": processing_id,
                "session_id": session_id,
                "user_id": user_id,
                "conversation_id": conversation_id,
            },
        )

        return processing_id

    async def get_processing_status(self, processing_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the current processing status.

        Args:
            processing_id: The processing ID to check

        Returns:
            Status information or None if not found
        """
        try:
            status_key = f"message_processing:{processing_id}"
            status_data = redis_service.get_data_from_redis(status_key, "status")
            return status_data
        except Exception as e:
            logger.error(
                f"Error retrieving processing status: {str(e)}",
                extra={"processing_id": processing_id, "error": str(e)},
            )
            return None

    async def _update_processing_status(
        self,
        processing_id: str,
        status: str,
        message: Optional[str] = None,
        task_id: Optional[str] = None,
        error: Optional[str] = None,
        created_at: Optional[str] = None,
        completed_at: Optional[str] = None,
    ) -> None:
        """
        Update the processing status in Redis.

        Args:
            processing_id: The processing ID
            status: The current status
            message: The agent response message (if completed)
            task_id: The task ID (if available)
            error: Error message (if failed)
            created_at: When processing started
            completed_at: When processing completed
        """
        try:
            status_data = {
                "processing_id": processing_id,
                "status": status,
                "message": message,
                "task_id": task_id,
                "error": error,
                "created_at": created_at,
                "completed_at": completed_at,
            }

            status_key = f"message_processing:{processing_id}"
            redis_service.set_data_to_redis(
                status_key, "status", status_data, expiry=3600  # 1 hour expiry
            )

        except Exception as e:
            logger.error(
                f"Error updating processing status: {str(e)}",
                extra={"processing_id": processing_id, "status": status, "error": str(e)},
            )

    async def _store_chat_messages(
        self, conversation_id: str, user_message: str, agent_response: str, user_id: str, attachments: Optional[List] = None, mode: Optional[ChatMode] = None
    ) -> None:
        """
        Store user and agent messages in the communication service.

        Args:
            conversation_id: The conversation ID
            user_message: The user's message content
            agent_response: The agent's response content
            user_id: The user ID
        """
        try:

            # # Store user message
            # if user_message:
            #     await communication_service.create_message(
            #         conversationId=conversation_id,
            #         senderType=SenderType.SENDER_TYPE_USER,
            #         data={"message": user_message, "attachments": attachments, "mode": mode},
            #         userId=user_id,
            #     )

            # Store agent response
            if agent_response:
                await communication_service.create_message(
                    conversationId=conversation_id,
                    senderType=SenderType.SENDER_TYPE_ASSISTANT,
                    data={"message": agent_response},
                    userId=user_id,
                )

            logger.info(
                "Chat messages stored successfully in background",
                extra={
                    "conversation_id": conversation_id,
                    "user_id": user_id,
                    "user_message_length": len(user_message) if user_message else 0,
                    "agent_response_length": len(agent_response) if agent_response else 0,
                },
            )

        except Exception as e:
            logger.error(
                f"Error storing chat messages in background: {str(e)}",
                extra={
                    "conversation_id": conversation_id,
                    "user_id": user_id,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
            )
            raise


# Global instance
background_task_manager = BackgroundTaskManager()
