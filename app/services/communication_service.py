from typing import Optional

import grpc
from fastapi import HTTPException
from google.protobuf import struct_pb2, wrappers_pb2
from google.protobuf.json_format import MessageToDict

from app.core.config import settings
from app.core.logging import get_logger
from app.grpc_ import communication_pb2, communication_pb2_grpc
from app.shared.constants import (
    CHAT_TYPE_TO_INT,
    INT_TO_CHAT_TYPE,
    INT_TO_SENDER_TYPE,
    INT_TO_TASK_STATUS,
    SENDER_TYPE_TO_INT,
    TASK_STATUS_TO_INT,
    ChatType,
    SenderType,
    TaskStatus,
)

# Reference wrapper types to ensure registration for Any unpacking
_ = (
    wrappers_pb2.StringValue,
    wrappers_pb2.Int64Value,
    wrappers_pb2.DoubleValue,
    wrappers_pb2.BoolValue,
    struct_pb2.Struct,
    struct_pb2.ListValue,
)

# Initialize logger
logger = get_logger(__name__)


class CommunicationServiceClient:
    def __init__(self):
        self.channel = grpc.insecure_channel(
            f"{settings.COMMUNICATION_SERVICE_HOST}:{settings.COMMUNICATION_SERVICE_PORT}"
        )
        self.stub = communication_pb2_grpc.CommunicationServiceStub(self.channel)

    def _handle_error(self, e: grpc.RpcError):
        status_code = e.code()
        details = e.details()

        if status_code == grpc.StatusCode.NOT_FOUND:
            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.ALREADY_EXISTS:
            raise HTTPException(status_code=409, detail=details)
        elif status_code == grpc.StatusCode.UNAUTHENTICATED:
            raise HTTPException(status_code=401, detail=details)
        elif status_code == grpc.StatusCode.PERMISSION_DENIED:
            raise HTTPException(status_code=403, detail=details)
        else:
            raise HTTPException(status_code=500, detail=details)

    def _convert_grpc_response_enums(self, response_dict):
        """Convert integer enum values in the gRPC response to string enum values."""
        if "chatType" in response_dict and isinstance(response_dict["chatType"], int):
            response_dict["chatType"] = INT_TO_CHAT_TYPE.get(
                response_dict["chatType"], ChatType.CHAT_TYPE_UNSPECIFIED
            )

        if "senderType" in response_dict and isinstance(response_dict["senderType"], int):
            response_dict["senderType"] = INT_TO_SENDER_TYPE.get(
                response_dict["senderType"], SenderType.SENDER_TYPE_UNSPECIFIED
            )

        if "taskStatus" in response_dict and isinstance(response_dict["taskStatus"], int):
            response_dict["taskStatus"] = INT_TO_TASK_STATUS.get(
                response_dict["taskStatus"], TaskStatus.TASK_STATUS_UNSPECIFIED
            )

        # Handle tasks field in conversations (repeated Task field)
        if "tasks" in response_dict and isinstance(response_dict["tasks"], list):
            for task in response_dict["tasks"]:
                if isinstance(task, dict):
                    self._convert_grpc_response_enums(task)

        return response_dict

    def _process_conversations_list(self, response_dict):
        """Process conversation list to convert enum values and ensure complete metadata."""
        # Ensure data key exists
        if "data" not in response_dict:
            response_dict["data"] = []
        elif isinstance(response_dict["data"], list):
            for conv in response_dict["data"]:
                self._convert_grpc_response_enums(conv)
                # Ensure tasks field is properly initialized if not present
                if "tasks" not in conv:
                    conv["tasks"] = []

        # Ensure metadata has all required fields
        if "metadata" not in response_dict:
            response_dict["metadata"] = {
                "total": 0,
                "totalPages": 0,
                "currentPage": 1,
                "pageSize": 10,
                "hasNextPage": False,
                "hasPreviousPage": False,
            }
        else:
            metadata = response_dict["metadata"]
            metadata.setdefault("total", 0)
            metadata.setdefault("totalPages", 0)
            metadata.setdefault("currentPage", 1)
            metadata.setdefault("pageSize", 10)
            metadata.setdefault("hasNextPage", False)
            metadata.setdefault("hasPreviousPage", False)

        return response_dict

    def _process_messages_list(self, response_dict):
        """Process message list to convert enum values and ensure complete metadata."""
        # Ensure data key exists
        if "data" not in response_dict:
            response_dict["data"] = []
        elif isinstance(response_dict["data"], list):
            for msg in response_dict["data"]:
                self._convert_grpc_response_enums(msg)

        # Ensure metadata has all required fields
        if "metadata" not in response_dict:
            response_dict["metadata"] = {
                "total": 0,
                "totalPages": 0,
                "currentPage": 1,
                "pageSize": 10,
                "hasNextPage": False,
                "hasPreviousPage": False,
            }
        else:
            metadata = response_dict["metadata"]
            metadata.setdefault("total", 0)
            metadata.setdefault("totalPages", 0)
            metadata.setdefault("currentPage", 1)
            metadata.setdefault("pageSize", 10)
            metadata.setdefault("hasNextPage", False)
            metadata.setdefault("hasPreviousPage", False)

        return response_dict

    def _process_tasks_list(self, response_dict):
        """Process task list to convert enum values and ensure complete metadata."""
        # Ensure data key exists
        if "data" not in response_dict:
            response_dict["data"] = []
        elif isinstance(response_dict["data"], list):
            for task in response_dict["data"]:
                self._convert_grpc_response_enums(task)

        # Ensure metadata has all required fields
        if "metadata" not in response_dict:
            response_dict["metadata"] = {
                "total": 0,
                "totalPages": 0,
                "currentPage": 1,
                "pageSize": 10,
                "hasNextPage": False,
                "hasPreviousPage": False,
            }
        else:
            metadata = response_dict["metadata"]
            metadata.setdefault("total", 0)
            metadata.setdefault("totalPages", 0)
            metadata.setdefault("currentPage", 1)
            metadata.setdefault("pageSize", 10)
            metadata.setdefault("hasNextPage", False)
            metadata.setdefault("hasPreviousPage", False)

        return response_dict

    def _process_task_response(self, response_dict):
        """Process single task response to convert enum values and ensure required fields."""
        # Convert enum values
        self._convert_grpc_response_enums(response_dict)

        # Ensure taskStatus is present for task responses
        if "taskStatus" not in response_dict:
            logger.warning("taskStatus field missing from task response, setting default")
            response_dict["taskStatus"] = TaskStatus.TASK_STATUS_UNSPECIFIED

        return response_dict

    def _dict_to_struct(self, data_dict):
        """Convert a Python dictionary to a protobuf Struct."""
        if data_dict is None:
            return None

        struct = struct_pb2.Struct()
        struct.update(data_dict)
        return struct

    async def create_conversation(
        self,
        userId: str,
        chatType: ChatType,
        agentId: Optional[str] = None,
    ):
        # Convert string enum to integer for gRPC
        chat_type_int = CHAT_TYPE_TO_INT.get(chatType, 0)

        request = communication_pb2.CreateConversationRequest(
            userId=userId,
            agentId=agentId,
            chatType=chat_type_int,
        )

        try:
            response = self.stub.createConversation(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            logger.debug("Create conversation response", extra={"response": response_dict})
            # Convert integer enums to string enums and ensure tasks field
            result = self._convert_grpc_response_enums(response_dict)
            # Ensure tasks field is properly initialized if not present
            if "tasks" not in result:
                result["tasks"] = []
            return result
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def get_conversation(self, conversationId: str, userId: str):
        request = communication_pb2.GetConversationRequest(
            conversationId=conversationId, userId=userId
        )

        try:
            response = self.stub.getConversation(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            # Convert integer enums to string enums and ensure tasks field
            result = self._convert_grpc_response_enums(response_dict)
            # Ensure tasks field is properly initialized if not present
            if "tasks" not in result:
                result["tasks"] = []
            return result
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_conversation(self, conversationId: str, userId: str):
        request = communication_pb2.DeleteConversationRequest(
            conversationId=conversationId, userId=userId
        )

        try:
            response = self.stub.deleteConversation(request)
            return MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_conversations(
        self,
        userId: str,
        chatType: ChatType,
        page: int = 1,
        limit: int = 10,
        agentId: Optional[str] = None,
    ):
        # Convert string enum to integer for gRPC
        chat_type_int = CHAT_TYPE_TO_INT.get(chatType, 0)

        request = communication_pb2.ListConversationsRequest(
            userId=userId,
            chatType=chat_type_int,
            page=page,
            limit=limit,
            agentId=agentId,
        )

        try:
            response = self.stub.listConversations(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            # Debug log the raw response
            logger.debug("Raw listConversations response", extra={"response": response_dict})
            # Process conversation list to convert enum values
            processed_response = self._process_conversations_list(response_dict)
            logger.debug(
                "Processed listConversations response", extra={"response": processed_response}
            )
            return processed_response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_conversation_tokens(
        self,
        conversationId: str,
        userId: str,
        inputTokens: Optional[int] = None,
        outputTokens: Optional[int] = None,
    ):
        request = communication_pb2.UpdateConversationTokensRequest(
            conversationId=conversationId,
            userId=userId,
            inputTokens=inputTokens,
            outputTokens=outputTokens,
        )

        try:
            response = self.stub.updateConversationTokens(request)
            return MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def create_message(
        self,
        conversationId: str,
        senderType: SenderType,
        userId: str,
        data: Optional[dict] = None,
        workflowId: Optional[str] = None,
        workflowResponse: Optional[dict] = None,
    ):
        # Convert string enums to integers for gRPC
        sender_type_int = SENDER_TYPE_TO_INT.get(senderType, 0)

        # Convert data dict to protobuf Struct if provided
        data_struct = self._dict_to_struct(data) if data is not None else None

        request = communication_pb2.CreateMessageRequest(
            conversationId=conversationId,
            senderType=sender_type_int,
            data=data_struct,
            workflowId=workflowId,
            workflowResponse=workflowResponse,
            userId=userId,
        )

        try:
            response = self.stub.createMessage(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            # Convert integer enums to string enums
            return self._convert_grpc_response_enums(response_dict)
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_message(self, messageId: str, userId: str):
        request = communication_pb2.DeleteMessageRequest(messageId=messageId, userId=userId)

        try:
            response = self.stub.deleteMessage(request)
            return MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_messages(self, conversationId: str, userId: str, page: int = 1, limit: int = 10):
        request = communication_pb2.ListMessagesRequest(
            conversationId=conversationId, page=page, limit=limit, userId=userId
        )

        try:
            response = self.stub.listMessages(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            # Debug log the raw response
            logger.debug("Raw listMessages response", extra={"response": response_dict})
            # Process messages list to convert enum values
            processed_response = self._process_messages_list(response_dict)
            logger.debug("Processed listMessages response", extra={"response": processed_response})
            return processed_response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def create_task(
        self,
        globalChatConversationId: str,
        title: str,
        agentConversationId: str,
        agentId: str,
        userId: str,
        taskStatus: TaskStatus,
        correlationId: Optional[str] = None,
        sessionId: Optional[str] = None,
    ):
        # Convert string enum to integer for gRPC
        task_status_int = TASK_STATUS_TO_INT.get(taskStatus, 0)

        request = communication_pb2.CreateTaskRequest(
            globalChatConversationId=globalChatConversationId,
            title=title,
            agentConversationId=agentConversationId,
            agentId=agentId,
            correlationId=correlationId,
            taskStatus=task_status_int,
            sessionId=sessionId,
            userId=userId,
        )

        try:
            response = self.stub.createTask(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            logger.debug("Create task response", extra={"response": response_dict})
            # Process task response to convert enum values and ensure required fields
            return self._process_task_response(response_dict)
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def update_task_status(self, taskId: str, taskStatus: TaskStatus, userId: str):
        # Convert string enum to integer for gRPC
        task_status_int = TASK_STATUS_TO_INT.get(taskStatus, 0)

        request = communication_pb2.UpdateTaskStatusRequest(
            taskId=taskId,
            taskStatus=task_status_int,
            userId=userId,
        )

        try:
            response = self.stub.updateTaskStatus(request)
            return MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def delete_task(self, taskId: str, userId: str):
        request = communication_pb2.DeleteTaskRequest(taskId=taskId, userId=userId)

        try:
            response = self.stub.deleteTask(request)
            return MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def list_tasks(
        self, globalChatConversationId: str, userId: str, page: int = 1, limit: int = 10
    ):
        request = communication_pb2.ListTasksRequest(
            globalChatConversationId=globalChatConversationId,
            page=page,
            limit=limit,
            userId=userId,
        )

        try:
            response = self.stub.listTasks(request)
            response_dict = MessageToDict(
                response, preserving_proto_field_name=True, use_integers_for_enums=True
            )
            # Debug log the raw response
            logger.debug("Raw listTasks response", extra={"response": response_dict})
            # Process tasks list to convert enum values
            processed_response = self._process_tasks_list(response_dict)
            logger.debug("Processed listTasks response", extra={"response": processed_response})
            return processed_response
        except grpc.RpcError as e:
            raise self._handle_error(e)

    async def task_delegation(
        self,
        title: str,
        globalChatConversationId: str,
        agentId: str,
        userId: str,
        correlationId: Optional[str] = None,
        sessionId: Optional[str] = None,
    ):
        """
        Delegate a task to an agent by creating a new agent conversation and task.

        This method performs two operations:
        1. Creates a new conversation for the specified agent
        2. Creates a task linking the global chat conversation to the agent conversation

        Args:
            title: The title/description of the task
            globalChatConversationId: ID of the global chat conversation
            agentId: ID of the agent to delegate the task to
            userId: ID of the user creating the task
            correlationId: Optional correlation ID for tracking
            sessionId: Optional session ID for the task

        Returns:
            dict: Contains both the created conversation and task data

        Raises:
            HTTPException: If either conversation or task creation fails
        """
        try:
            # Step 1: Create a new conversation for the agent
            logger.info(
                "Creating agent conversation for task delegation",
                extra={
                    "agentId": agentId,
                    "userId": userId,
                    "globalChatConversationId": globalChatConversationId,
                },
            )

            agent_conversation = await self.create_conversation(
                userId=userId,
                chatType=ChatType.CHAT_TYPE_AGENT,
                agentId=agentId,
            )

            agent_conversation_id = agent_conversation.get("id")
            if not agent_conversation_id:
                logger.error("Failed to get conversation ID from agent conversation creation")
                raise HTTPException(
                    status_code=500,
                    detail="Failed to create agent conversation - no conversation ID returned",
                )

            logger.info(
                "Agent conversation created successfully",
                extra={
                    "agentConversationId": agent_conversation_id,
                    "agentId": agentId,
                },
            )

            # Step 2: Create the task linking global chat to agent conversation
            logger.info(
                "Creating task for delegation",
                extra={
                    "title": title,
                    "globalChatConversationId": globalChatConversationId,
                    "agentConversationId": agent_conversation_id,
                    "correlationId": correlationId,
                    "sessionId": sessionId,
                },
            )

            task = await self.create_task(
                globalChatConversationId=globalChatConversationId,
                title=title,
                agentConversationId=agent_conversation_id,
                agentId=agentId,
                userId=userId,
                taskStatus=TaskStatus.TASK_STATUS_RUNNING,
                correlationId=correlationId,
                sessionId=sessionId,
            )

            logger.info(
                "Task delegation completed successfully",
                extra={
                    "taskId": task.get("id"),
                    "agentConversationId": agent_conversation_id,
                    "title": title,
                },
            )

            # Return both the conversation and task data
            return {
                "success": True,
                "message": "Task delegated successfully",
                "data": {
                    "task": task,
                    "agentConversation": agent_conversation,
                },
            }

        except HTTPException:
            # Re-raise HTTP exceptions as they are already properly formatted
            raise
        except Exception as e:
            logger.error(
                "Unexpected error during task delegation",
                extra={
                    "error": str(e),
                    "agentId": agentId,
                    "globalChatConversationId": globalChatConversationId,
                },
                exc_info=True,
            )
            raise HTTPException(status_code=500, detail=f"Failed to delegate task: {str(e)}")
