"""
Event handler service for processing various SSE events.

This module provides a centralized event handler that processes different types
of events and routes them to the appropriate SSE manager methods.
"""

from typing import Any, Dict

from app.core.logging import get_logger
from app.helper.sse_manager import SseManager
from app.shared.constants import ContentType, SSEEventType

logger = get_logger(__name__)


class EventHandler:
    """
    Centralized event handler for processing SSE events.

    This class provides methods to handle different types of events
    and route them to the appropriate SSE manager methods.
    """

    def __init__(self, sse_manager: SseManager):
        """
        Initialize the event handler.

        Args:
            sse_manager: The SSE manager instance to use for sending events
        """
        self.sse_manager = sse_manager

    async def handle_event(self, event_data: Dict[str, Any]) -> bool:
        """
        Handle an event based on its type.

        Args:
            event_data: The event data containing event_type and other fields

        Returns:
            True if the event was handled, False otherwise
        """
        event_type = event_data.get("event_type")
        session_id = event_data.get("session_id")
        client_id = session_id  # Use session_id as client_id

        if not event_type:
            logger.warning("Event data missing event_type")
            return False

        try:
            # Session management events
            if event_type == SSEEventType.SESSION_INITIALIZED.value:
                return await self._handle_session_initialized(event_data, client_id)

            # Message streaming events
            elif event_type == SSEEventType.MESSAGE_STREAM_STARTED.value:
                return await self._handle_message_stream_started(event_data, client_id)

            elif event_type == SSEEventType.MESSAGE_STREAMING.value:
                return await self._handle_message_streaming(event_data, client_id)

            elif event_type == SSEEventType.MESSAGE_END.value:
                return await self._handle_message_end(event_data, client_id)

            elif event_type == SSEEventType.MESSAGE_RESPONSE.value:
                return await self._handle_message_response(event_data, client_id)

            # MCP (Model Context Protocol) events
            elif event_type == SSEEventType.MCP_EXECUTION_STARTED.value:
                return await self._handle_mcp_execution_started(event_data, client_id)

            elif event_type == SSEEventType.MCP_EXECUTION_ENDED.value:
                return await self._handle_mcp_execution_ended(event_data, client_id)

            elif event_type == SSEEventType.MCP_EXECUTION_FAILED.value:
                return await self._handle_mcp_execution_failed(event_data, client_id)

            # Workflow execution events
            elif event_type == SSEEventType.WORKFLOW_EXECUTION_STARTED.value:
                return await self._handle_workflow_execution_started(event_data, client_id)

            elif event_type == SSEEventType.WORKFLOW_EXECUTION_STEP.value:
                return await self._handle_workflow_execution_step(event_data, client_id)

            elif event_type == SSEEventType.WORKFLOW_EXECUTION_COMPLETED.value:
                return await self._handle_workflow_execution_completed(event_data, client_id)

            elif event_type == SSEEventType.WORKFLOW_EXECUTION_FAILED.value:
                return await self._handle_workflow_execution_failed(event_data, client_id)

            # Knowledge fetch events
            elif event_type == SSEEventType.KNOWLEDGE_FETCH_STARTED.value:
                return await self._handle_knowledge_fetch_started(event_data, client_id)

            elif event_type == SSEEventType.KNOWLEDGE_FETCH_COMPLETED.value:
                return await self._handle_knowledge_fetch_completed(event_data, client_id)

            elif event_type == SSEEventType.KNOWLEDGE_FETCH_FAILED.value:
                return await self._handle_knowledge_fetch_failed(event_data, client_id)

            else:
                logger.warning(f"Unknown event type: {event_type}")
                return False

        except Exception as e:
            logger.error(f"Error handling event {event_type}: {e}")
            return False

    async def _handle_session_initialized(self, event_data: Dict[str, Any], client_id: str) -> bool:
        """Handle session initialized event."""
        session_id = event_data.get("session_id")
        if session_id:
            self.sse_manager.send_session_initialized(session_id=session_id, client_id=client_id)
            return True
        return False

    async def _handle_message_stream_started(
        self, event_data: Dict[str, Any], client_id: str
    ) -> bool:
        """Handle message stream started event."""
        self.sse_manager.send_message_stream_started(client_id=client_id)
        return True

    async def _handle_message_streaming(self, event_data: Dict[str, Any], client_id: str) -> bool:
        """Handle message streaming event."""
        agent_response = event_data.get("agent_response", {})
        content_type = event_data.get("content_type", ContentType.TEXT_PLAIN.value)

        self.sse_manager.send_message_streaming(
            content=agent_response.get("content", ""),
            content_type=content_type,
            message_type=agent_response.get("message_type", "streaming_chunk"),
            source=agent_response.get("source", "assistant"),
            metadata=agent_response.get("metadata", {}),
            client_id=client_id,
        )
        return True

    async def _handle_message_end(self, event_data: Dict[str, Any], client_id: str) -> bool:
        """Handle message end event."""
        session_id = event_data.get("session_id")
        if session_id:
            self.sse_manager.send_message_end(session_id=session_id, client_id=client_id)
            return True
        return False

    async def _handle_message_response(self, event_data: Dict[str, Any], client_id: str) -> bool:
        """Handle message response event."""
        content_type = event_data.get("content_type", ContentType.TEXT_PLAIN.value)

        self.sse_manager.send_message_response(
            content=event_data.get("agent_response", ""),
            content_type=content_type,
            client_id=client_id,
        )
        return True

    async def _handle_mcp_execution_started(
        self, event_data: Dict[str, Any], client_id: str
    ) -> bool:
        """Handle MCP execution started event."""
        execution_id = event_data.get("execution_id", "")
        self.sse_manager.send_mcp_execution_started(execution_id=execution_id, client_id=client_id)
        return True

    async def _handle_mcp_execution_ended(self, event_data: Dict[str, Any], client_id: str) -> bool:
        """Handle MCP execution ended event."""
        execution_id = event_data.get("execution_id", "")
        result = event_data.get("result")
        self.sse_manager.send_mcp_execution_ended(
            execution_id=execution_id, result=result, client_id=client_id
        )
        return True

    async def _handle_mcp_execution_failed(
        self, event_data: Dict[str, Any], client_id: str
    ) -> bool:
        """Handle MCP execution failed event."""
        execution_id = event_data.get("execution_id", "")
        error = event_data.get("error")
        self.sse_manager.send_mcp_execution_failed(
            execution_id=execution_id, error=error, client_id=client_id
        )
        return True

    async def _handle_workflow_execution_started(
        self, event_data: Dict[str, Any], client_id: str
    ) -> bool:
        """Handle workflow execution started event."""
        workflow_id = event_data.get("workflow_id", "")
        correlation_id = event_data.get("correlation_id", "")
        message = event_data.get("message", "")
        success = event_data.get("success", False)
        steps = event_data.get("steps", [])

        self.sse_manager.send_workflow_execution_started(
            workflow_id=workflow_id,
            correlation_id=correlation_id,
            message=message,
            success=success,
            steps=steps,
            client_id=client_id,
        )
        return True

    async def _handle_workflow_execution_step(
        self, event_data: Dict[str, Any], client_id: str
    ) -> bool:
        """Handle workflow execution step event."""
        workflow_id = event_data.get("workflow_id", "")
        execution_id = event_data.get("execution_id", "")
        step_id = event_data.get("step_id", "")
        step_name = event_data.get("step_name", "")
        step_status = event_data.get("step_status", "")
        result = event_data.get("result")

        self.sse_manager.send_workflow_execution_step(
            workflow_id=workflow_id,
            execution_id=execution_id,
            step_id=step_id,
            step_name=step_name,
            step_status=step_status,
            result=result,
            client_id=client_id,
        )
        return True

    async def _handle_workflow_execution_completed(
        self, event_data: Dict[str, Any], client_id: str
    ) -> bool:
        """Handle workflow execution completed event."""
        workflow_id = event_data.get("workflow_id", "")
        execution_id = event_data.get("execution_id", "")
        result = event_data.get("result")

        self.sse_manager.send_workflow_execution_completed(
            workflow_id=workflow_id,
            execution_id=execution_id,
            result=result,
            client_id=client_id,
        )
        return True

    async def _handle_workflow_execution_failed(
        self, event_data: Dict[str, Any], client_id: str
    ) -> bool:
        """Handle workflow execution failed event."""
        workflow_id = event_data.get("workflow_id", "")
        execution_id = event_data.get("execution_id", "")
        error = event_data.get("error")

        self.sse_manager.send_workflow_execution_failed(
            workflow_id=workflow_id,
            execution_id=execution_id,
            error=error,
            client_id=client_id,
        )
        return True

    async def _handle_knowledge_fetch_started(
        self, event_data: Dict[str, Any], client_id: str
    ) -> bool:
        """Handle knowledge fetch started event."""
        fetch_id = event_data.get("fetch_id", "")
        query = event_data.get("query")

        self.sse_manager.send_knowledge_fetch_started(
            fetch_id=fetch_id, query=query, client_id=client_id
        )
        return True

    async def _handle_knowledge_fetch_completed(
        self, event_data: Dict[str, Any], client_id: str
    ) -> bool:
        """Handle knowledge fetch completed event."""
        fetch_id = event_data.get("fetch_id", "")
        result = event_data.get("result")

        self.sse_manager.send_knowledge_fetch_completed(
            fetch_id=fetch_id, result=result, client_id=client_id
        )
        return True

    async def _handle_knowledge_fetch_failed(
        self, event_data: Dict[str, Any], client_id: str
    ) -> bool:
        """Handle knowledge fetch failed event."""
        fetch_id = event_data.get("fetch_id", "")
        error = event_data.get("error")

        self.sse_manager.send_knowledge_fetch_failed(
            fetch_id=fetch_id, error=error, client_id=client_id
        )
        return True
