import queue
import threading
import time
from typing import Any, Dict, Optional

from app.core.logging import get_logger
from app.shared.constants import ContentType, SSEEventType

logger = get_logger(__name__)


class SseManager:
    """
    Advanced Server-Sent Events (SSE) management class
    Handles client-specific and broadcast event distribution
    """

    _instance = None

    def __new__(cls):
        if not cls._instance:
            cls._instance = super(SseManager, cls).__new__(cls)
            cls._instance.initialize()
        return cls._instance

    def initialize(self):
        # Thread-safe dictionary for connected clients
        self.connected_clients: Dict[str, queue.Queue] = {}
        self.clients_lock = threading.Lock()
        # Shutdown flag
        self.is_shutting_down = False

    def add_client(self, client_id: str, client_queue: queue.Queue):
        """
        Register a new client connection
        """
        with self.clients_lock:
            self.connected_clients[client_id] = client_queue
        logger.info(f"New client connected: {client_id}")

    def remove_client(self, client_id: str):
        """
        Remove a client connection
        """
        with self.clients_lock:
            if client_id in self.connected_clients:
                del self.connected_clients[client_id]
                logger.info(f"Client disconnected: {client_id}")

    def send_update(self, event_name: str, event_type: str, data: Any, client_id: str = None):
        """
        Send an update to specific client or broadcast

        :param event_name: Name of the event
        :param event_type: Type of the event
        :param data: Event data
        :param client_id: Optional specific client ID to send to
        """
        event = {
            "event": event_name,
            "data": data,
            "type": event_type,
            "timestamp": time.time(),
            "client_id": client_id,  # Include client_id in the event
        }

        with self.clients_lock:
            # If client_id is specified, send to that specific client
            if client_id and client_id in self.connected_clients:
                try:
                    self.connected_clients[client_id].put(event, block=False)
                    logger.info(f"Event sent to specific client: {client_id}")
                except queue.Full:
                    logger.warning(f"Client {client_id} queue is full, skipping event")
            # If no client_id, broadcast to all clients
            else:
                for client_queue in self.connected_clients.values():
                    try:
                        client_queue.put(event, block=False)
                    except queue.Full:
                        logger.warning("A client queue is full, skipping event")

    def clear_clients(self):
        """
        Clear all connected clients
        """
        with self.clients_lock:
            self.connected_clients.clear()
        logger.info("All clients cleared")

    def shutdown(self):
        """
        Graceful shutdown of SSE manager
        """
        self.is_shutting_down = True
        self.clear_clients()
        logger.info("SSE Manager shutting down")

    # Convenience methods for specific event types
    def send_session_initialized(self, session_id: str, client_id: str = None):
        """Send session initialized event."""
        self.send_update(
            event_name=SSEEventType.SESSION_INITIALIZED.value,
            event_type="session",
            data={"session_id": session_id, "status": "initialized"},
            client_id=client_id,
        )

    def send_message_stream_started(self, client_id: str = None):
        """Send message stream started event."""
        self.send_update(
            event_name=SSEEventType.MESSAGE_STREAM_STARTED.value,
            event_type="message",
            data={"status": "started"},
            client_id=client_id,
        )

    def send_message_response(
        self,
        content: str,
        content_type: ContentType = ContentType.TEXT_PLAIN,
        client_id: str = None,
    ):
        """Send message streaming chunk event."""
        data = {
            "content": content,
            "content_type": content_type,
        }

        self.send_update(
            event_name=SSEEventType.MESSAGE_RESPONSE.value,
            event_type="message",
            data=data,
            client_id=client_id,
        )

    def send_message_streaming(
        self,
        content: str,
        content_type: ContentType = ContentType.TEXT_PLAIN,
        message_type: str = "streaming_chunk",
        source: str = "assistant",
        metadata: Optional[Dict[str, Any]] = {},
        client_id: str = None,
    ):
        """Send message streaming chunk event."""
        data = {
            "content": content,
            "content_type": content_type,
            "message_type": message_type,
            "source": source,
            "metadata": metadata,
        }

        self.send_update(
            event_name=SSEEventType.MESSAGE_STREAMING.value,
            event_type="message",
            data=data,
            client_id=client_id,
        )

    def send_message_end(self, session_id: str, client_id: str = None):
        """Send message end event."""
        self.send_update(
            event_name=SSEEventType.MESSAGE_END.value,
            event_type="message",
            data={"session_id": session_id, "status": "completed"},
            client_id=client_id,
        )

    def send_mcp_execution_started(self, execution_id: str, client_id: str = None):
        """Send MCP execution started event."""
        self.send_update(
            event_name=SSEEventType.MCP_EXECUTION_STARTED.value,
            event_type="mcp",
            data={"execution_id": execution_id, "status": "started"},
            client_id=client_id,
        )

    def send_mcp_execution_ended(
        self, execution_id: str, result: Optional[Dict[str, Any]] = None, client_id: str = None
    ):
        """Send MCP execution ended event."""
        data = {"execution_id": execution_id, "status": "completed"}
        if result:
            data["result"] = result

        self.send_update(
            event_name=SSEEventType.MCP_EXECUTION_ENDED.value,
            event_type="mcp",
            data=data,
            client_id=client_id,
        )

    def send_workflow_execution_started(
        self,
        workflow_id: str,
        execution_id: str,
        steps: Optional[list] = None,
        client_id: str = None,
    ):
        """Send workflow execution started event."""
        self.send_update(
            event_name=SSEEventType.WORKFLOW_EXECUTION_STARTED,
            event_type="workflow",
            data={
                "workflow_id": workflow_id,
                "execution_id": execution_id,
                "status": "started",
                "steps": steps or [],
            },
            client_id=client_id,
        )

    def send_error(
        self,
        error_code: str,
        error_message: str,
        details: Optional[Dict[str, Any]] = None,
        client_id: str = None,
    ):
        """Send error event."""
        data = {"error_code": error_code, "error_message": error_message}
        if details:
            data["details"] = details

        self.send_update(
            event_name=SSEEventType.ERROR.value, event_type="error", data=data, client_id=client_id
        )
